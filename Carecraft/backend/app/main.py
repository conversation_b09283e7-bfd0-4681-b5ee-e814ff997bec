"""
FastAPI Server for Bosch eCall Emergency Communication System

This module exposes the LangGraph workflow via REST API endpoints
for handling eCall emergency sessions.
"""

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uuid
from datetime import datetime

from agent.workflow import run_workflow_step, get_workflow_visualization
from agent.state import create_initial_state, AgentState

app = FastAPI(
    title="Bosch eCall Emergency Communication System",
    description="AI-driven emergency communication system for vehicle incidents",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",
        "http://localhost:3001",  # Alternative React port
        "http://localhost:8080",  # Alternative frontend port
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory session storage (in production, use Redis or database)
sessions: Dict[str, AgentState] = {}


class SessionStartRequest(BaseModel):
    """Request model for starting a new eCall session."""
    initial_message: Optional[str] = ""
    vehicle_info: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    customer_info: Optional[Dict[str, Any]] = None


class SessionMessageRequest(BaseModel):
    """Request model for sending a message in an existing session."""
    message: str


class SessionResponse(BaseModel):
    """Response model for eCall session interactions."""
    session_id: str
    message: str
    is_complete: bool
    incident_type: Optional[str] = None
    next_action: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None


@app.get("/")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "Bosch eCall Emergency Communication System",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/workflow/visualization")
def get_workflow_info():
    """Get information about the workflow structure."""
    return {
        "workflow_structure": get_workflow_visualization(),
        "supported_incident_types": [
            "INJURY_ACCIDENT",
            "LIGHT_ACCIDENT",
            "RSA_NEED",
            "ROAD_HAZARD",
            "UNKNOWN"
        ],
        "available_actions": [
            "ASK_ABOUT_INJURIES",
            "CONFIRM_LOCATION",
            "CONTACT_PSAP",
            "CONTACT_RSA",
            "ASK_FOR_CLARIFICATION",
            "END_INTERACTION"
        ]
    }


@app.post("/e-call/session", response_model=SessionResponse)
def start_ecall_session(request: SessionStartRequest):
    """
    Initialize a new eCall session and return the agent's first message.

    This endpoint creates a new emergency communication session, sets up
    the initial state with any provided context, and returns the agent's
    greeting message along with a unique session ID.
    """

    try:
        # Generate unique session ID
        session_id = str(uuid.uuid4())

        # Create initial state
        initial_state = create_initial_state(session_id, request.initial_message)

        # Add context information if provided
        context = {}
        if request.vehicle_info:
            context["vehicle_info"] = request.vehicle_info
        if request.location:
            context["location"] = request.location
        if request.customer_info:
            context["customer_info"] = request.customer_info

        initial_state["context"] = context

        # Run the workflow to get the initial response
        updated_state = run_workflow_step(initial_state)

        # Store the session state
        sessions[session_id] = updated_state

        # Return the response
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start eCall session: {str(e)}"
        )


@app.post("/e-call/session/{session_id}", response_model=SessionResponse)
def continue_ecall_session(session_id: str, request: SessionMessageRequest):
    """
    Continue an existing eCall session with a user message.

    This endpoint takes the user's reply, loads the state for the session,
    runs it through the LangGraph workflow, saves the new state, and
    returns the agent's response.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        # Get current session state
        current_state = sessions[session_id]

        # Check if session is already complete
        if current_state.get("is_complete", False):
            return SessionResponse(
                session_id=session_id,
                message="This emergency session has already been completed. If you need additional assistance, please start a new session.",
                is_complete=True,
                incident_type=current_state.get("incident_type"),
                next_action=current_state.get("next_action"),
                metrics=current_state.get("metrics")
            )

        # Update state with new user input
        current_state["user_input"] = request.message

        # Run the workflow with updated state
        updated_state = run_workflow_step(current_state)

        # Update the stored session state
        sessions[session_id] = updated_state

        # Return the response
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process message: {str(e)}"
        )


@app.get("/e-call/session/{session_id}/status")
def get_session_status(session_id: str):
    """
    Get the current status of an eCall session.

    This endpoint returns the current state information for a session
    including conversation history, metrics, and completion status.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        current_state = sessions[session_id]

        return {
            "session_id": session_id,
            "is_complete": current_state.get("is_complete", False),
            "incident_type": current_state.get("incident_type"),
            "next_action": current_state.get("next_action"),
            "conversation_length": len(current_state.get("history", [])),
            "metrics": current_state.get("metrics", {}),
            "last_updated": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get session status: {str(e)}"
        )


@app.delete("/e-call/session/{session_id}")
def end_session(session_id: str):
    """
    End and clean up an eCall session.

    This endpoint removes the session from memory and returns
    a summary of the session.
    """

    try:
        # Check if session exists
        if session_id not in sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Session {session_id} not found"
            )

        # Get session data before deletion
        session_data = sessions[session_id]

        # Remove session from memory
        del sessions[session_id]

        return {
            "message": "Session ended successfully",
            "session_id": session_id,
            "final_incident_type": session_data.get("incident_type"),
            "conversation_length": len(session_data.get("history", [])),
            "was_completed": session_data.get("is_complete", False),
            "metrics": session_data.get("metrics", {}),
            "ended_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to end session: {str(e)}"
        )


@app.get("/e-call/sessions")
def list_active_sessions():
    """
    List all currently active eCall sessions.

    This endpoint returns a summary of all sessions currently in memory.
    Useful for monitoring and debugging purposes.
    """

    try:
        session_summaries = []

        for session_id, state in sessions.items():
            session_summaries.append({
                "session_id": session_id,
                "incident_type": state.get("incident_type"),
                "is_complete": state.get("is_complete", False),
                "conversation_length": len(state.get("history", [])),
                "start_time": state.get("metrics", {}).get("call_start_time"),
                "last_activity": datetime.now().isoformat()  # Simplified for demo
            })

        return {
            "active_sessions": len(sessions),
            "sessions": session_summaries,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list sessions: {str(e)}"
        )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)