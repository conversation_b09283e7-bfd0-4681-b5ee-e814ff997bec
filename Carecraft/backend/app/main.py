import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

# from services.config import API_KEY

# from api.agent import router as agent_router

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",
        "http://localhost:3001",  # Alternative React port
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


@app.get("/")
def health_check():
    return {"status": "healthy"}


@app.get("/test")
def test_endpoint():
    return {"GOOGLE_API_KEY": GOOGLE_API_KEY}


app.include_router(agent_router, prefix="", tags=["agent"])


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)