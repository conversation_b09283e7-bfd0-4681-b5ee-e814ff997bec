"""
FastAPI Server for Bosch eCall Emergency Communication System
"""

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uuid
from datetime import datetime

from agent.workflow import run_workflow_step, get_workflow_visualization
from agent.state import create_initial_state, AgentState

app = FastAPI(
    title="Bosch eCall Emergency Communication System",
    description="AI-driven emergency communication system for vehicle incidents",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://localhost:8080",
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory session storage
sessions: Dict[str, AgentState] = {}

class SessionStartRequest(BaseModel):
    initial_message: Optional[str] = ""
    vehicle_info: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    customer_info: Optional[Dict[str, Any]] = None

class SessionMessageRequest(BaseModel):
    message: str

class SessionResponse(BaseModel):
    session_id: str
    message: str
    is_complete: bool
    incident_type: Optional[str] = None
    next_action: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None

@app.get("/")
def health_check():
    return {
        "status": "healthy",
        "service": "Bosch eCall Emergency Communication System",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/e-call/session", response_model=SessionResponse)
def start_ecall_session(request: SessionStartRequest):
    try:
        session_id = str(uuid.uuid4())
        initial_state = create_initial_state(session_id, request.initial_message)
        
        context = {}
        if request.vehicle_info:
            context["vehicle_info"] = request.vehicle_info
        if request.location:
            context["location"] = request.location
        if request.customer_info:
            context["customer_info"] = request.customer_info
        
        initial_state["context"] = context
        updated_state = run_workflow_step(initial_state)
        sessions[session_id] = updated_state
        
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start eCall session: {str(e)}")

@app.post("/e-call/session/{session_id}", response_model=SessionResponse)
def continue_ecall_session(session_id: str, request: SessionMessageRequest):
    try:
        if session_id not in sessions:
            raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
        
        current_state = sessions[session_id]
        
        if current_state.get("is_complete", False):
            return SessionResponse(
                session_id=session_id,
                message="This emergency session has already been completed.",
                is_complete=True,
                incident_type=current_state.get("incident_type"),
                next_action=current_state.get("next_action"),
                metrics=current_state.get("metrics")
            )
        
        current_state["user_input"] = request.message
        updated_state = run_workflow_step(current_state)
        sessions[session_id] = updated_state
        
        return SessionResponse(
            session_id=session_id,
            message=updated_state.get("final_response", ""),
            is_complete=updated_state.get("is_complete", False),
            incident_type=updated_state.get("incident_type"),
            next_action=updated_state.get("next_action"),
            metrics=updated_state.get("metrics")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process message: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)