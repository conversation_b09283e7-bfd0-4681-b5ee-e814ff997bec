# example router file
import time
import random
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from langchain_core.messages import HumanMessage
from langchain_core.messages import ToolMessage

router = APIRouter()



class AnalyzeRequest(BaseModel):
    text: str
    alreadyHighlighted: Optional[list] = []
    method: Optional[str] = "llm_guided_highlighting"
    options: Optional[Dict[str, Any]] = {}


@router.post("/agent_highlight/")
async def agent_highlight(request: AnalyzeRequest):
    """
    Endpoint to invoke the agent for text analysis and highlighting.
    """

    # llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0, max_output_tokens=20000)
    # llm_with_tools = llm.bind_tools([create_text_highlights])

    max_retries = 3
    base_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            query = request.text

            # print(request.alreadyHighlighted)
            # already_highlighted = request.alreadyHighlighted

            # already_highlighted_spans = [x["text_span"] for x in already_highlighted]
            # already_highlighted_string = ", ".join(already_highlighted_spans)

            # hydrated_prompt = create_analysis_prompt(query, already_highlighted_string)

            # messages = [HumanMessage(hydrated_prompt)]

            # ai_msg = llm_with_tools.invoke(messages)

            # for tool_call in ai_msg.tool_calls:
            #     selected_tool = {"create_text_highlights": create_text_highlights}[tool_call["name"].lower()]
            #     tool_msg = selected_tool.invoke(tool_call["args"])

            # highlights = tool_msg
            # print(f"Tool call results: {highlights}")

            return {
                "status": "success",
                # "method": request.method,
                # "results": highlights,
                "ai_analysis": "Analysis completed using LLM-based highlighting"            }

        except Exception as e:
            print(f"Attempt {attempt + 1} failed with error: {e}")

            if attempt == max_retries - 1:  # Last attempt
                print(f"All {max_retries} attempts failed")
                raise HTTPException(status_code=500, detail=str(e))

            # Exponential backoff with jitter
            delay = base_delay * (2**attempt) + random.uniform(0, 1)
            print(f"Retrying in {delay:.2f} seconds...")
            time.sleep(delay)

