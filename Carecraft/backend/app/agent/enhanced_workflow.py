"""
Enhanced Conversational Workflow for Bosch eCall Emergency Communication System

This module defines the new LangGraph StateGraph workflow that orchestrates
the enhanced conversational emergency communication process. The workflow 
supports empathetic interactions, systematic data gathering, user confirmations,
and multi-request handling through a series of connected nodes.
"""

from typing import Literal

try:
    from langgraph.graph import StateGraph, END
    from langgraph.graph.graph import CompiledGraph
    LANGGRAPH_AVAILABLE = True
except ImportError:
    # Mock LangGraph for testing without dependencies
    LANGGRAPH_AVAILABLE = False
    class StateGraph:
        def __init__(self, state_type): pass
        def add_node(self, name, func): pass
        def set_entry_point(self, name): pass
        def add_conditional_edges(self, source, condition, mapping): pass
        def add_edge(self, source, target): pass
        def compile(self): return MockCompiledGraph()

    class MockCompiledGraph:
        def invoke(self, state):
            # Simple mock workflow execution
            if not state.get("final_response"):
                state["final_response"] = "Mock enhanced conversational response for testing"
                state["is_complete"] = True
            return state

    END = "END"

try:
    from .state import AgentState, IncidentType, NextAction, ConversationPhase
    from .conversational_nodes import (
        empathetic_greeting, clarification_loop, systematic_data_gathering,
        confirmation_request, execute_emergency_action, follow_up_and_additional_help
    )
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from agent.state import AgentState, IncidentType, NextAction, ConversationPhase
    from agent.conversational_nodes import (
        empathetic_greeting, clarification_loop, systematic_data_gathering,
        confirmation_request, execute_emergency_action, follow_up_and_additional_help
    )


def route_from_greeting(state: AgentState) -> Literal["clarification_loop"]:
    """
    Route from greeting to clarification phase.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    return "clarification_loop"


def route_from_clarification(state: AgentState) -> Literal[
    "clarification_loop", "systematic_data_gathering"
]:
    """
    Route from clarification based on classification confidence.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    
    # If we have a classified incident with good confidence, move to data gathering
    incident_type = state.get("incident_type")
    confidence = state.get("classification_confidence", 0.0)
    
    if incident_type and confidence >= 0.6:
        return "systematic_data_gathering"
    else:
        # Need more clarification
        return "clarification_loop"


def route_from_data_gathering(state: AgentState) -> Literal[
    "systematic_data_gathering", "confirmation_request"
]:
    """
    Route from data gathering based on completion status.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    
    # Check if we have more data to gather
    from .state import get_next_data_item
    next_item = get_next_data_item(state)
    
    if next_item is not None:
        # Still have data to gather
        return "systematic_data_gathering"
    else:
        # All data gathered, move to confirmation
        return "confirmation_request"


def route_from_confirmation(state: AgentState) -> Literal[
    "confirmation_request", "execute_emergency_action", "clarification_loop"
]:
    """
    Route from confirmation based on user response.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    
    pending_confirmation = state.get("pending_confirmation")
    
    if pending_confirmation and pending_confirmation.get("is_confirmed") is True:
        # User confirmed, execute action
        return "execute_emergency_action"
    elif pending_confirmation and pending_confirmation.get("is_confirmed") is False:
        # User declined, go back to clarification
        return "clarification_loop"
    else:
        # Still waiting for confirmation
        return "confirmation_request"


def route_from_action_execution(state: AgentState) -> Literal["follow_up_and_additional_help"]:
    """
    Route from action execution to follow-up.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    return "follow_up_and_additional_help"


def route_from_follow_up(state: AgentState) -> Literal[
    "follow_up_and_additional_help", "clarification_loop", "END"
]:
    """
    Route from follow-up based on user's additional help needs.
    
    Args:
        state: Current agent state
    
    Returns:
        Next node name to execute
    """
    
    has_additional_requests = state.get("has_additional_requests")
    is_complete = state.get("is_complete", False)
    
    if is_complete:
        return "END"
    elif has_additional_requests is True:
        # User needs more help, start new request
        return "clarification_loop"
    elif has_additional_requests is False:
        # User doesn't need more help, end conversation
        return "END"
    else:
        # Still waiting for response about additional help
        return "follow_up_and_additional_help"


def create_enhanced_workflow() -> CompiledGraph:
    """
    Create and compile the enhanced conversational LangGraph workflow.
    
    Returns:
        CompiledGraph: The compiled workflow graph ready for execution
    """
    
    # Initialize the state graph with our enhanced AgentState
    workflow = StateGraph(AgentState)
    
    # Add all the conversational workflow nodes
    workflow.add_node("empathetic_greeting", empathetic_greeting)
    workflow.add_node("clarification_loop", clarification_loop)
    workflow.add_node("systematic_data_gathering", systematic_data_gathering)
    workflow.add_node("confirmation_request", confirmation_request)
    workflow.add_node("execute_emergency_action", execute_emergency_action)
    workflow.add_node("follow_up_and_additional_help", follow_up_and_additional_help)
    
    # Set the entry point to the empathetic greeting
    workflow.set_entry_point("empathetic_greeting")
    
    # Add conditional edges for conversational flow
    
    # From greeting, always go to clarification
    workflow.add_edge("empathetic_greeting", "clarification_loop")
    
    # From clarification, either continue clarifying or move to data gathering
    workflow.add_conditional_edges(
        "clarification_loop",
        route_from_clarification,
        {
            "clarification_loop": "clarification_loop",
            "systematic_data_gathering": "systematic_data_gathering"
        }
    )
    
    # From data gathering, either continue gathering or move to confirmation
    workflow.add_conditional_edges(
        "systematic_data_gathering",
        route_from_data_gathering,
        {
            "systematic_data_gathering": "systematic_data_gathering",
            "confirmation_request": "confirmation_request"
        }
    )
    
    # From confirmation, execute action, decline, or wait for response
    workflow.add_conditional_edges(
        "confirmation_request",
        route_from_confirmation,
        {
            "confirmation_request": "confirmation_request",
            "execute_emergency_action": "execute_emergency_action",
            "clarification_loop": "clarification_loop"
        }
    )
    
    # From action execution, always go to follow-up
    workflow.add_edge("execute_emergency_action", "follow_up_and_additional_help")
    
    # From follow-up, either handle more requests, end, or wait for response
    workflow.add_conditional_edges(
        "follow_up_and_additional_help",
        route_from_follow_up,
        {
            "follow_up_and_additional_help": "follow_up_and_additional_help",
            "clarification_loop": "clarification_loop",
            "END": END
        }
    )
    
    # Compile and return the workflow
    return workflow.compile()


# Create the compiled enhanced workflow instance
enhanced_compiled_workflow = create_enhanced_workflow()


def run_enhanced_workflow_step(state: AgentState) -> AgentState:
    """
    Execute one step of the enhanced conversational workflow.
    
    Args:
        state: Current agent state
    
    Returns:
        AgentState: Updated state after workflow execution
    """
    
    try:
        # Run the enhanced workflow with the current state
        result = enhanced_compiled_workflow.invoke(state)
        return result
    except Exception as e:
        # Handle workflow execution errors gracefully
        print(f"Enhanced workflow execution error: {str(e)}")
        
        # Return error state with empathetic message
        error_state = state.copy()
        error_state.update({
            "final_response": "I apologize, but I'm experiencing technical difficulties. Please don't worry - if this is an emergency, please call emergency services directly at your local emergency number. I'm here to help once the issue is resolved.",
            "is_complete": True,
            "requires_human_handoff": True
        })
        return error_state


def get_enhanced_workflow_visualization() -> str:
    """
    Get a text representation of the enhanced workflow structure.
    
    Returns:
        str: Text representation of the enhanced conversational workflow
    """
    
    return """
    Enhanced eCall Conversational Workflow Structure:
    
    START → empathetic_greeting
              ↓
         clarification_loop ←──────────────┐
              ↓                           │
         systematic_data_gathering        │
              ↓                           │
         confirmation_request             │
              ↓                           │
         execute_emergency_action         │
              ↓                           │
         follow_up_and_additional_help ───┘
              ↓
             END
             
    Key Features:
    - Empathetic, reassuring initial contact
    - Iterative clarification until confident classification
    - Systematic data gathering with checklists
    - User confirmation before critical actions
    - Multi-request handling in single session
    - Graceful conversation closure
    """
