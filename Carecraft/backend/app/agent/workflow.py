"""
LangGraph Workflow for Bosch eCall Emergency Communication System

This module implements the main workflow logic using LangGraph's StateGraph.
It orchestrates the conversation flow, incident classification, and appropriate
response routing for emergency situations.
"""

from typing import Dict, Any, Literal
from langgraph.graph import StateGraph, END
from langgraph.graph.graph import CompiledGraph

from .state import AgentState, IncidentType, NextAction
from .nodes import (
    start_interaction,
    classify_incident,
    process_injury_accident,
    process_light_accident,
    process_road_hazard,
    process_rsa_request,
    handle_unknown,
    end_interaction
)


def should_classify_incident(state: AgentState) -> Literal["classify", "start"]:
    """
    Conditional edge function to determine if we should classify the incident.
    
    Args:
        state: Current agent state
    
    Returns:
        str: Next node to execute ("classify" or "start")
    """
    
    user_input = state.get("user_input", "").strip()
    
    # If there's user input, proceed to classification
    if user_input:
        return "classify"
    
    # If no user input, start with greeting
    return "start"


def route_by_incident_type(state: AgentState) -> str:
    """
    Conditional edge function to route to appropriate processing node based on incident type.
    
    Args:
        state: Current agent state
    
    Returns:
        str: Next node name to execute
    """
    
    incident_type = state.get("incident_type")
    
    if incident_type == IncidentType.INJURY_ACCIDENT:
        return "process_injury_accident"
    elif incident_type == IncidentType.LIGHT_ACCIDENT:
        return "process_light_accident"
    elif incident_type == IncidentType.ROAD_HAZARD:
        return "process_road_hazard"
    elif incident_type == IncidentType.RSA_NEED:
        return "process_rsa_request"
    else:
        return "handle_unknown"


def should_end_interaction(state: AgentState) -> Literal["end", "classify"]:
    """
    Conditional edge function to determine if the interaction should end.
    
    Args:
        state: Current agent state
    
    Returns:
        str: Next node to execute ("end" or "classify")
    """
    
    # If the interaction is marked as complete, end it
    if state.get("is_complete", False):
        return "end"
    
    # If there's new user input and the interaction isn't complete, continue classifying
    if state.get("user_input", "").strip():
        return "classify"
    
    # Default to ending if no clear next step
    return "end"


def create_workflow() -> CompiledGraph:
    """
    Create and compile the LangGraph workflow for eCall emergency communication.
    
    Returns:
        CompiledGraph: Compiled workflow ready for execution
    """
    
    # Initialize the state graph
    workflow = StateGraph(AgentState)
    
    # Add nodes to the workflow
    workflow.add_node("start", start_interaction)
    workflow.add_node("classify", classify_incident)
    workflow.add_node("process_injury_accident", process_injury_accident)
    workflow.add_node("process_light_accident", process_light_accident)
    workflow.add_node("process_road_hazard", process_road_hazard)
    workflow.add_node("process_rsa_request", process_rsa_request)
    workflow.add_node("handle_unknown", handle_unknown)
    workflow.add_node("end", end_interaction)
    
    # Set the entry point
    workflow.set_entry_point("start")
    
    # Add conditional edges
    workflow.add_conditional_edges(
        "start",
        should_classify_incident,
        {
            "classify": "classify",
            "start": END  # If no input, end after greeting
        }
    )
    
    workflow.add_conditional_edges(
        "classify",
        route_by_incident_type,
        {
            "process_injury_accident": "process_injury_accident",
            "process_light_accident": "process_light_accident",
            "process_road_hazard": "process_road_hazard",
            "process_rsa_request": "process_rsa_request",
            "handle_unknown": "handle_unknown"
        }
    )
    
    # Add edges from processing nodes to end
    workflow.add_edge("process_injury_accident", END)
    workflow.add_edge("process_light_accident", END)
    workflow.add_edge("process_road_hazard", END)
    workflow.add_edge("process_rsa_request", END)
    
    # Handle unknown incidents - they can continue or end
    workflow.add_conditional_edges(
        "handle_unknown",
        should_end_interaction,
        {
            "end": "end",
            "classify": "classify"
        }
    )
    
    workflow.add_edge("end", END)
    
    # Compile and return the workflow
    return workflow.compile()


# Global workflow instance
_compiled_workflow = None


def get_workflow() -> CompiledGraph:
    """
    Get the compiled workflow instance (singleton pattern).
    
    Returns:
        CompiledGraph: The compiled workflow
    """
    
    global _compiled_workflow
    
    if _compiled_workflow is None:
        _compiled_workflow = create_workflow()
    
    return _compiled_workflow


def run_workflow_step(state: AgentState) -> AgentState:
    """
    Execute one step of the workflow with the given state.
    
    This function runs the workflow and returns the updated state.
    It's designed to handle both initial interactions and continuing conversations.
    
    Args:
        state: Current agent state
    
    Returns:
        AgentState: Updated state after workflow execution
    """
    
    try:
        workflow = get_workflow()
        
        # Execute the workflow
        result = workflow.invoke(state)
        
        # Ensure the result is a valid AgentState
        if isinstance(result, dict):
            # Merge the result with the original state to ensure all fields are present
            updated_state = state.copy()
            updated_state.update(result)
            return updated_state
        else:
            # If result is not a dict, return the original state with an error
            error_state = state.copy()
            error_state["final_response"] = "I apologize, but I encountered an error processing your request. Please try again."
            return error_state
            
    except Exception as e:
        # Handle any workflow execution errors
        error_state = state.copy()
        error_state["final_response"] = f"I apologize, but I encountered an error: {str(e)}. Please try again or contact support."
        error_state["is_complete"] = True
        return error_state


def get_workflow_visualization() -> Dict[str, Any]:
    """
    Get a visualization/description of the workflow structure.
    
    Returns:
        Dict containing workflow structure information
    """
    
    return {
        "nodes": [
            {
                "name": "start",
                "description": "Entry point - greets user and asks for situation details",
                "type": "entry"
            },
            {
                "name": "classify",
                "description": "Classifies incident type based on user input using keyword matching",
                "type": "decision"
            },
            {
                "name": "process_injury_accident",
                "description": "Handles serious accidents with injuries - contacts PSAP immediately",
                "type": "action"
            },
            {
                "name": "process_light_accident",
                "description": "Handles minor accidents without injuries - contacts PSAP with lower priority",
                "type": "action"
            },
            {
                "name": "process_road_hazard",
                "description": "Handles road hazard reports - notifies authorities",
                "type": "action"
            },
            {
                "name": "process_rsa_request",
                "description": "Handles roadside assistance requests - contacts RSA provider",
                "type": "action"
            },
            {
                "name": "handle_unknown",
                "description": "Fallback for unclear situations - asks for clarification",
                "type": "clarification"
            },
            {
                "name": "end",
                "description": "Completes the interaction with closing message",
                "type": "terminal"
            }
        ],
        "edges": [
            {"from": "start", "to": "classify", "condition": "has_user_input"},
            {"from": "classify", "to": "process_injury_accident", "condition": "incident_type == INJURY_ACCIDENT"},
            {"from": "classify", "to": "process_light_accident", "condition": "incident_type == LIGHT_ACCIDENT"},
            {"from": "classify", "to": "process_road_hazard", "condition": "incident_type == ROAD_HAZARD"},
            {"from": "classify", "to": "process_rsa_request", "condition": "incident_type == RSA_NEED"},
            {"from": "classify", "to": "handle_unknown", "condition": "incident_type == UNKNOWN"},
            {"from": "handle_unknown", "to": "classify", "condition": "has_new_input"},
            {"from": "handle_unknown", "to": "end", "condition": "should_end"}
        ],
        "flow_description": "The workflow starts by greeting the user, then classifies their input to determine the type of emergency. Based on the classification, it routes to the appropriate handler (PSAP for accidents/hazards, RSA for roadside assistance, or clarification for unclear situations). Each handler provides specialized responses and contacts the relevant services."
    }
