"""
State Management for Bosch eCall Emergency Communication System

This module defines the state structure and helper functions for the LangGraph workflow.
The state tracks conversation history, incident classification, and metrics throughout
the emergency communication session.
"""

from typing import TypedDict, List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
import uuid


class IncidentType(str, Enum):
    """Enumeration of possible incident types that can be classified."""
    INJURY_ACCIDENT = "INJURY_ACCIDENT"
    LIGHT_ACCIDENT = "LIGHT_ACCIDENT"
    RSA_NEED = "RSA_NEED"
    ROAD_HAZARD = "ROAD_HAZARD"
    UNKNOWN = "UNKNOWN"


class NextAction(str, Enum):
    """Enumeration of possible next actions in the workflow."""
    ASK_ABOUT_INJURIES = "ASK_ABOUT_INJURIES"
    CONFIRM_LOCATION = "CONFIRM_LOCATION"
    CONTACT_PSAP = "CONTACT_PSAP"
    CONTACT_RSA = "CONTACT_RSA"
    ASK_FOR_CLARIFICATION = "ASK_FOR_CLARIFICATION"
    END_INTERACTION = "END_INTERACTION"


class ConversationEntry(TypedDict):
    """Structure for individual conversation entries."""
    timestamp: str
    user_message: str
    agent_response: str
    action_taken: Optional[str]


class AgentState(TypedDict):
    """
    Main state structure for the eCall emergency communication workflow.
    
    This TypedDict defines all the information that flows through the LangGraph
    workflow nodes, tracking the conversation state, classification results,
    and operational metrics.
    """
    
    # Session Management
    session_id: str
    timestamp: str
    
    # Conversation Flow
    user_input: str
    final_response: str
    history: List[ConversationEntry]
    
    # Classification Results
    incident_type: Optional[IncidentType]
    next_action: Optional[NextAction]
    
    # Context Information
    context: Dict[str, Any]  # Vehicle info, location, customer details
    
    # Workflow Control
    is_complete: bool
    
    # Performance Metrics
    metrics: Dict[str, Any]


def create_initial_state(session_id: str, initial_message: str = "") -> AgentState:
    """
    Create a new AgentState with default values for starting a session.
    
    Args:
        session_id: Unique identifier for the session
        initial_message: Optional initial message from the user
    
    Returns:
        AgentState: Initialized state dictionary
    """
    
    return AgentState(
        session_id=session_id,
        timestamp=datetime.now().isoformat(),
        user_input=initial_message,
        final_response="",
        history=[],
        incident_type=None,
        next_action=None,
        context={},
        is_complete=False,
        metrics={
            "session_start_time": datetime.now().isoformat(),
            "classification_confidence": 0.0,
            "response_count": 0,
            "psap_contact_time": None,
            "rsa_contact_time": None,
            "resolution_time": None
        }
    )


def add_to_history(state: AgentState, user_message: str, agent_response: str, 
                   action_taken: Optional[str] = None) -> AgentState:
    """
    Add a new conversation entry to the state history.
    
    Args:
        state: Current agent state
        user_message: The user's message
        agent_response: The agent's response
        action_taken: Optional description of action taken
    
    Returns:
        AgentState: Updated state with new history entry
    """
    
    new_entry = ConversationEntry(
        timestamp=datetime.now().isoformat(),
        user_message=user_message,
        agent_response=agent_response,
        action_taken=action_taken
    )
    
    # Create a copy of the state with updated history
    updated_state = state.copy()
    updated_history = state.get("history", []).copy()
    updated_history.append(new_entry)
    updated_state["history"] = updated_history
    
    # Increment response count
    updated_metrics = state.get("metrics", {}).copy()
    updated_metrics["response_count"] = updated_metrics.get("response_count", 0) + 1
    updated_state["metrics"] = updated_metrics
    
    return updated_state


def update_metrics(state: AgentState, metric_name: str, metric_value: Any) -> AgentState:
    """
    Update a specific metric in the state.
    
    Args:
        state: Current agent state
        metric_name: Name of the metric to update
        metric_value: New value for the metric
    
    Returns:
        AgentState: Updated state with new metric value
    """
    
    updated_state = state.copy()
    updated_metrics = state.get("metrics", {}).copy()
    updated_metrics[metric_name] = metric_value
    updated_state["metrics"] = updated_metrics
    
    return updated_state


def get_conversation_summary(state: AgentState) -> Dict[str, Any]:
    """
    Generate a summary of the conversation for reporting purposes.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict containing conversation summary information
    """
    
    history = state.get("history", [])
    metrics = state.get("metrics", {})
    
    return {
        "session_id": state.get("session_id"),
        "incident_type": state.get("incident_type"),
        "total_exchanges": len(history),
        "session_duration": _calculate_session_duration(metrics),
        "resolution_status": "COMPLETE" if state.get("is_complete") else "IN_PROGRESS",
        "classification_confidence": metrics.get("classification_confidence", 0.0),
        "services_contacted": _get_services_contacted(metrics),
        "final_action": state.get("next_action")
    }


def _calculate_session_duration(metrics: Dict[str, Any]) -> Optional[str]:
    """
    Calculate the duration of the session based on start and end times.
    
    Args:
        metrics: Metrics dictionary from state
    
    Returns:
        Optional[str]: Duration string or None if not calculable
    """
    
    start_time_str = metrics.get("session_start_time")
    end_time_str = metrics.get("resolution_time")
    
    if not start_time_str or not end_time_str:
        return None
    
    try:
        start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
        duration = end_time - start_time
        return str(duration)
    except (ValueError, TypeError):
        return None


def _get_services_contacted(metrics: Dict[str, Any]) -> List[str]:
    """
    Determine which external services were contacted during the session.
    
    Args:
        metrics: Metrics dictionary from state
    
    Returns:
        List[str]: List of service names that were contacted
    """
    
    services = []
    
    if metrics.get("psap_contact_time"):
        services.append("PSAP")
    
    if metrics.get("rsa_contact_time"):
        services.append("RSA")
    
    return services


def validate_state(state: AgentState) -> bool:
    """
    Validate that the state contains all required fields.
    
    Args:
        state: State to validate
    
    Returns:
        bool: True if state is valid, False otherwise
    """
    
    required_fields = [
        "session_id", "timestamp", "user_input", "final_response", 
        "history", "context", "is_complete", "metrics"
    ]
    
    for field in required_fields:
        if field not in state:
            return False
    
    return True
