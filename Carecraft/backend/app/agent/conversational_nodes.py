"""
Enhanced Conversational Workflow Nodes for Bosch eCall Emergency Communication System

This module contains the new conversational workflow nodes that provide empathetic,
systematic, and robust emergency communication handling. These nodes implement
the enhanced conversation flow with clarification loops, data gathering checklists,
user confirmations, and multi-request handling.
"""

from typing import Dict, Any, Optional
from datetime import datetime
import random

try:
    from .state import (
        AgentState, IncidentType, NextAction, ConversationPhase, DataGatheringStatus,
        add_to_history, update_metrics, transition_phase, create_data_gathering_checklist,
        update_data_gathering_item, get_next_data_item, create_confirmation_request
    )
    from ..services.psap_service import contact_psap
    from ..services.rsa_service import contact_rsa
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from agent.state import (
        AgentState, IncidentType, NextAction, ConversationPhase, DataGatheringStatus,
        add_to_history, update_metrics, transition_phase, create_data_gathering_checklist,
        update_data_gathering_item, get_next_data_item, create_confirmation_request
    )
    from services.psap_service import contact_psap
    from services.rsa_service import contact_rsa


# Empathetic response templates
EMPATHY_RESPONSES = {
    "HIGH": [
        "I understand this is a very stressful situation, and I'm here to help you through it.",
        "I can hear that you're concerned, and that's completely understandable. Let me assist you.",
        "This sounds like a difficult situation. I'm going to make sure you get the help you need.",
    ],
    "CRITICAL": [
        "I understand this is an emergency situation. Please stay calm - I'm here to help you immediately.",
        "I can hear the urgency in your situation. I'm taking action right now to get you help.",
        "This is clearly a serious situation. I'm going to prioritize getting you emergency assistance.",
    ],
    "MEDIUM": [
        "I understand you need assistance, and I'm here to help you.",
        "Thank you for contacting us. I'll make sure we get this resolved for you.",
        "I can help you with this situation. Let me gather some information to assist you better.",
    ],
    "LOW": [
        "I'm here to help you with your situation.",
        "Let me assist you with this matter.",
        "I'll be happy to help you resolve this.",
    ]
}

REASSURANCE_PHRASES = [
    "You're doing great by reaching out for help.",
    "I'm going to take care of this for you.",
    "Help is on the way.",
    "You're in good hands now.",
    "I'm here with you through this.",
    "We're going to get this resolved.",
]


def empathetic_greeting(state: AgentState) -> Dict[str, Any]:
    """
    Enhanced greeting node that provides empathetic, reassuring initial contact.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values
    """
    
    # Assess initial stress level from user input
    user_input = state.get("user_input", "").lower()
    stress_indicators = ["emergency", "urgent", "help", "hurt", "accident", "crash", "serious", "critical"]
    stress_level = "HIGH" if any(indicator in user_input for indicator in stress_indicators) else "MEDIUM"
    
    # Select appropriate empathetic greeting
    empathy_response = random.choice(EMPATHY_RESPONSES[stress_level])
    
    greeting_message = (
        f"Hello, this is Bosch Emergency Services. {empathy_response} "
        f"To help you as quickly as possible, could you please tell me what's happening?"
    )
    
    # Update state with greeting and transition to clarification phase
    updated_state = transition_phase(state, ConversationPhase.CLARIFICATION)
    updated_state = add_to_history(updated_state, "", greeting_message, phase=ConversationPhase.GREETING)
    updated_state["user_stress_level"] = stress_level
    updated_state["empathy_responses_used"] = [empathy_response]
    
    return {
        "final_response": greeting_message,
        "current_phase": ConversationPhase.CLARIFICATION,
        "user_stress_level": stress_level,
        "empathy_responses_used": [empathy_response],
        "next_action": NextAction.ASK_FOR_CLARIFICATION,
        "history": updated_state["history"],
        "metrics": updated_state["metrics"]
    }


def clarification_loop(state: AgentState) -> Dict[str, Any]:
    """
    Handles clarification when the incident type is unclear or confidence is low.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values
    """
    
    user_input = state.get("user_input", "").lower()
    
    # Check if we have enough information to classify
    if not user_input.strip():
        clarification_message = (
            "I want to make sure I understand your situation correctly. "
            "Could you please describe what's happening in a bit more detail?"
        )
        
        updated_state = add_to_history(state, user_input, clarification_message, 
                                     phase=ConversationPhase.CLARIFICATION)
        
        return {
            "final_response": clarification_message,
            "next_action": NextAction.ASK_FOR_CLARIFICATION,
            "history": updated_state["history"]
        }
    
    # Attempt classification with enhanced keyword matching
    incident_type, confidence = _enhanced_classify_incident(user_input)
    
    if confidence < 0.6:  # Low confidence, ask for clarification
        clarification_questions = {
            "general": "To help you better, could you tell me: Are you reporting an accident, a vehicle breakdown, or a road hazard?",
            "accident": "I understand there may have been an accident. Is anyone injured or hurt?",
            "vehicle": "I see you're having vehicle trouble. Is this a mechanical problem like a flat tire or dead battery?",
            "hazard": "You mentioned a road hazard. Is this something blocking the road or creating a dangerous situation?"
        }
        
        # Choose appropriate clarification question
        question_key = "general"
        if "accident" in user_input or "crash" in user_input:
            question_key = "accident"
        elif "car" in user_input or "vehicle" in user_input or "tire" in user_input or "battery" in user_input:
            question_key = "vehicle"
        elif "road" in user_input or "hazard" in user_input or "debris" in user_input:
            question_key = "hazard"
        
        clarification_message = clarification_questions[question_key]
        
        updated_state = add_to_history(state, user_input, clarification_message, 
                                     phase=ConversationPhase.CLARIFICATION)
        
        return {
            "final_response": clarification_message,
            "incident_type": incident_type if confidence > 0.3 else None,
            "classification_confidence": confidence,
            "next_action": NextAction.ASK_FOR_CLARIFICATION,
            "history": updated_state["history"]
        }
    
    # High confidence classification - move to data gathering
    updated_state = transition_phase(state, ConversationPhase.DATA_GATHERING)
    
    # Create data gathering checklist
    checklist = create_data_gathering_checklist(incident_type)
    updated_state["data_gathering_checklist"] = checklist
    updated_state["incident_type"] = incident_type
    updated_state["classification_confidence"] = confidence
    
    # Provide reassurance and transition message
    reassurance = random.choice(REASSURANCE_PHRASES)
    transition_message = (
        f"Thank you for that information. {reassurance} "
        f"I've identified this as a {incident_type.value.replace('_', ' ').lower()} situation. "
        f"I need to gather a few important details to get you the right help."
    )
    
    updated_state = add_to_history(updated_state, user_input, transition_message, 
                                 phase=ConversationPhase.DATA_GATHERING, confidence_level=confidence)
    
    return {
        "final_response": transition_message,
        "current_phase": ConversationPhase.DATA_GATHERING,
        "incident_type": incident_type,
        "classification_confidence": confidence,
        "data_gathering_checklist": checklist,
        "next_action": NextAction.GATHER_INCIDENT_DETAILS,
        "history": updated_state["history"],
        "metrics": updated_state["metrics"]
    }


def systematic_data_gathering(state: AgentState) -> Dict[str, Any]:
    """
    Systematically gathers required information using the checklist approach.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values
    """
    
    user_input = state.get("user_input", "").strip()
    current_item_name = state.get("current_data_item")
    
    # If we have user input and we're expecting a response to a specific question
    if user_input and current_item_name:
        # Update the current data item with the user's response
        updated_state = update_data_gathering_item(state, current_item_name, user_input)
    else:
        updated_state = state.copy()
    
    # Get the next item that needs to be collected
    next_item = get_next_data_item(updated_state)
    
    if next_item is None:
        # All required data has been gathered - move to confirmation phase
        updated_state = transition_phase(updated_state, ConversationPhase.CONFIRMATION)
        
        completion_message = (
            "Thank you for providing all that information. "
            "Based on what you've told me, I'm ready to take action to help you."
        )
        
        updated_state = add_to_history(updated_state, user_input, completion_message, 
                                     phase=ConversationPhase.CONFIRMATION)
        
        return {
            "final_response": completion_message,
            "current_phase": ConversationPhase.CONFIRMATION,
            "current_data_item": None,
            "next_action": NextAction.CONFIRM_ACTION,
            "history": updated_state["history"],
            "metrics": updated_state["metrics"]
        }
    
    # Ask the next question
    question_message = next_item["question"]
    
    # Add empathetic context if this is a sensitive question
    if "injury" in next_item["field_name"] or "hurt" in next_item["question"].lower():
        empathy_prefix = "I know this might be difficult to talk about, but "
        question_message = empathy_prefix + question_message.lower()
    
    updated_state = add_to_history(updated_state, user_input, question_message, 
                                 phase=ConversationPhase.DATA_GATHERING)
    
    # Update the current data item being processed
    updated_checklist = []
    for item in updated_state.get("data_gathering_checklist", []):
        if item["field_name"] == next_item["field_name"]:
            updated_item = item.copy()
            updated_item["status"] = DataGatheringStatus.IN_PROGRESS
            updated_checklist.append(updated_item)
        else:
            updated_checklist.append(item)
    
    return {
        "final_response": question_message,
        "current_data_item": next_item["field_name"],
        "data_gathering_checklist": updated_checklist,
        "next_action": NextAction.GATHER_INCIDENT_DETAILS,
        "history": updated_state["history"]
    }


def _enhanced_classify_incident(user_input: str) -> tuple[IncidentType, float]:
    """
    Enhanced incident classification with confidence scoring.
    
    Args:
        user_input: User's input text (already lowercased)
    
    Returns:
        Tuple of (IncidentType, confidence_score)
    """
    
    # Enhanced keyword patterns with weights
    injury_patterns = {
        "hurt": 2, "injured": 3, "pain": 2, "bleeding": 3, "unconscious": 4,
        "broken": 3, "fracture": 3, "ambulance": 4, "hospital": 3, "medical": 2,
        "emergency": 2, "serious": 2, "critical": 3, "severe": 3
    }
    
    accident_patterns = {
        "crash": 3, "accident": 3, "collision": 3, "hit": 2, "impact": 2,
        "airbag": 3, "damage": 2, "fender bender": 2, "rear end": 2, "side impact": 3
    }
    
    rsa_patterns = {
        "flat tire": 4, "tire": 2, "battery": 3, "dead battery": 4, "won't start": 3,
        "engine": 2, "breakdown": 3, "mechanical": 3, "tow": 3, "stuck": 2,
        "lockout": 4, "locked out": 4, "keys": 2, "fuel": 2, "gas": 2, "empty tank": 3
    }
    
    hazard_patterns = {
        "debris": 3, "object": 2, "road": 1, "hazard": 3, "dangerous": 2,
        "blocking": 3, "obstacle": 3, "fallen tree": 4, "rocks": 3, "construction": 2, "pothole": 2
    }
    
    # Calculate weighted scores
    injury_score = sum(weight for pattern, weight in injury_patterns.items() if pattern in user_input)
    accident_score = sum(weight for pattern, weight in accident_patterns.items() if pattern in user_input)
    rsa_score = sum(weight for pattern, weight in rsa_patterns.items() if pattern in user_input)
    hazard_score = sum(weight for pattern, weight in hazard_patterns.items() if pattern in user_input)
    
    # Determine incident type and confidence
    scores = {
        IncidentType.INJURY_ACCIDENT: injury_score + (accident_score * 0.5),
        IncidentType.LIGHT_ACCIDENT: accident_score if injury_score == 0 else 0,
        IncidentType.RSA_NEED: rsa_score,
        IncidentType.ROAD_HAZARD: hazard_score
    }
    
    max_score = max(scores.values())
    
    if max_score == 0:
        return IncidentType.UNKNOWN, 0.0
    
    incident_type = max(scores, key=scores.get)
    confidence = min(max_score / 10.0, 1.0)  # Normalize to 0-1 range
    
    return incident_type, confidence


def confirmation_request(state: AgentState) -> Dict[str, Any]:
    """
    Requests user confirmation before taking critical actions.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    incident_type = state.get("incident_type")
    user_input = state.get("user_input", "").lower().strip()

    # Check if we're responding to a previous confirmation request
    pending_confirmation = state.get("pending_confirmation")
    if pending_confirmation and user_input:
        # Process the user's response to confirmation
        is_confirmed = _parse_confirmation_response(user_input)

        if is_confirmed:
            # User confirmed - proceed with action
            updated_state = transition_phase(state, ConversationPhase.ACTION_EXECUTION)
            confirmed_actions = state.get("confirmed_actions", []).copy()
            confirmed_actions.append(pending_confirmation["action_description"])

            confirmation_message = (
                "Thank you for confirming. I'm taking action now to get you the help you need. "
                "Please stay on the line while I coordinate your assistance."
            )

            updated_state = add_to_history(updated_state, user_input, confirmation_message,
                                         phase=ConversationPhase.ACTION_EXECUTION)

            return {
                "final_response": confirmation_message,
                "current_phase": ConversationPhase.ACTION_EXECUTION,
                "pending_confirmation": None,
                "confirmed_actions": confirmed_actions,
                "next_action": NextAction.CONTACT_PSAP if incident_type in [IncidentType.INJURY_ACCIDENT, IncidentType.LIGHT_ACCIDENT, IncidentType.ROAD_HAZARD] else NextAction.CONTACT_RSA,
                "history": updated_state["history"],
                "metrics": updated_state["metrics"]
            }
        else:
            # User declined - ask what they'd prefer
            decline_message = (
                "I understand your concern. What would you prefer I do to help you? "
                "I can provide you with information to handle this yourself, or we can discuss other options."
            )

            updated_state = add_to_history(state, user_input, decline_message,
                                         phase=ConversationPhase.CONFIRMATION)

            return {
                "final_response": decline_message,
                "pending_confirmation": None,
                "next_action": NextAction.ASK_FOR_CLARIFICATION,
                "history": updated_state["history"]
            }

    # Create new confirmation request
    if incident_type == IncidentType.INJURY_ACCIDENT:
        action_desc = "contact emergency services immediately"
        confirmation_msg = (
            "Based on your situation, I need to contact emergency services right away to dispatch "
            "medical assistance and police to your location. Is that okay with you?"
        )
    elif incident_type == IncidentType.LIGHT_ACCIDENT:
        action_desc = "contact emergency services for accident reporting"
        confirmation_msg = (
            "I'd like to contact emergency services to report your accident and get you assistance "
            "with the proper documentation. Should I proceed with that?"
        )
    elif incident_type == IncidentType.ROAD_HAZARD:
        action_desc = "report the road hazard to authorities"
        confirmation_msg = (
            "I'll report this road hazard to the appropriate authorities so they can address it "
            "and keep other drivers safe. Is that what you'd like me to do?"
        )
    elif incident_type == IncidentType.RSA_NEED:
        action_desc = "contact roadside assistance for you"
        confirmation_msg = (
            "I can contact our roadside assistance service to send a technician to help with your "
            "vehicle problem. Would you like me to arrange that for you?"
        )
    else:
        action_desc = "provide general assistance"
        confirmation_msg = (
            "I'd like to help you with your situation. Can you tell me what specific assistance "
            "you're looking for?"
        )

    # Create confirmation request
    confirmation_request_obj = create_confirmation_request(action_desc, confirmation_msg)

    updated_state = add_to_history(state, "", confirmation_msg, phase=ConversationPhase.CONFIRMATION)

    # Update metrics
    updated_metrics = state.get("metrics", {}).copy()
    updated_metrics["confirmation_requests"] = updated_metrics.get("confirmation_requests", 0) + 1

    return {
        "final_response": confirmation_msg,
        "pending_confirmation": confirmation_request_obj,
        "next_action": NextAction.CONFIRM_ACTION,
        "history": updated_state["history"],
        "metrics": updated_metrics
    }


def execute_emergency_action(state: AgentState) -> Dict[str, Any]:
    """
    Executes the confirmed emergency action (PSAP or RSA contact).

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    incident_type = state.get("incident_type")

    if incident_type in [IncidentType.INJURY_ACCIDENT, IncidentType.LIGHT_ACCIDENT, IncidentType.ROAD_HAZARD]:
        return _execute_psap_contact(state)
    elif incident_type == IncidentType.RSA_NEED:
        return _execute_rsa_contact(state)
    else:
        # Fallback for unknown incidents
        error_message = (
            "I apologize, but I need more information to help you effectively. "
            "Could you please describe your situation again?"
        )

        updated_state = transition_phase(state, ConversationPhase.CLARIFICATION)
        updated_state = add_to_history(updated_state, "", error_message,
                                     phase=ConversationPhase.CLARIFICATION)

        return {
            "final_response": error_message,
            "current_phase": ConversationPhase.CLARIFICATION,
            "next_action": NextAction.ASK_FOR_CLARIFICATION,
            "history": updated_state["history"],
            "metrics": updated_state["metrics"]
        }


def follow_up_and_additional_help(state: AgentState) -> Dict[str, Any]:
    """
    Handles follow-up after action completion and asks about additional help.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    user_input = state.get("user_input", "").lower().strip()

    # Check if user is responding to "anything else" question
    if state.get("has_additional_requests") is None:
        # First time asking about additional help
        follow_up_message = (
            "Is there anything else I can help you with today? "
            "I'm here to assist with any other concerns you might have."
        )

        updated_state = transition_phase(state, ConversationPhase.FOLLOW_UP)
        updated_state = add_to_history(updated_state, "", follow_up_message,
                                     phase=ConversationPhase.FOLLOW_UP)

        return {
            "final_response": follow_up_message,
            "current_phase": ConversationPhase.FOLLOW_UP,
            "next_action": NextAction.ASK_FOR_MORE_HELP,
            "history": updated_state["history"],
            "metrics": updated_state["metrics"]
        }

    # Process user's response about additional help
    if user_input:
        needs_more_help = _parse_additional_help_response(user_input)

        if needs_more_help:
            # User needs more help - reset for new request
            additional_help_message = (
                "Of course! I'm happy to help with your additional concern. "
                "Please tell me what else you need assistance with."
            )

            # Reset state for new request but keep history
            updated_state = transition_phase(state, ConversationPhase.CLARIFICATION)
            updated_state["incident_type"] = None
            updated_state["classification_confidence"] = 0.0
            updated_state["data_gathering_checklist"] = []
            updated_state["current_data_item"] = None
            updated_state["pending_confirmation"] = None
            updated_state["has_additional_requests"] = True

            updated_state = add_to_history(updated_state, user_input, additional_help_message,
                                         phase=ConversationPhase.CLARIFICATION)

            return {
                "final_response": additional_help_message,
                "current_phase": ConversationPhase.CLARIFICATION,
                "incident_type": None,
                "classification_confidence": 0.0,
                "data_gathering_checklist": [],
                "current_data_item": None,
                "pending_confirmation": None,
                "has_additional_requests": True,
                "next_action": NextAction.ASK_FOR_CLARIFICATION,
                "history": updated_state["history"],
                "metrics": updated_state["metrics"]
            }
        else:
            # User doesn't need more help - move to closure
            updated_state = transition_phase(state, ConversationPhase.CLOSURE)
            updated_state["has_additional_requests"] = False

            closure_message = (
                "Thank you for using Bosch Emergency Services. I'm glad I could help you today. "
                "Please don't hesitate to contact us again if you need any assistance. Stay safe!"
            )

            updated_state = add_to_history(updated_state, user_input, closure_message,
                                         phase=ConversationPhase.CLOSURE)

            return {
                "final_response": closure_message,
                "current_phase": ConversationPhase.CLOSURE,
                "has_additional_requests": False,
                "is_complete": True,
                "next_action": NextAction.END_INTERACTION,
                "history": updated_state["history"],
                "metrics": updated_state["metrics"]
            }

    # No user input yet - wait for response
    return {
        "final_response": state.get("final_response", ""),
        "next_action": NextAction.ASK_FOR_MORE_HELP
    }


# Helper Functions

def _parse_confirmation_response(user_input: str) -> bool:
    """
    Parse user's response to determine if they confirmed or declined.

    Args:
        user_input: User's response (already lowercased)

    Returns:
        bool: True if confirmed, False if declined
    """

    positive_indicators = ["yes", "yeah", "yep", "ok", "okay", "sure", "go ahead", "proceed", "do it", "please"]
    negative_indicators = ["no", "nope", "don't", "stop", "wait", "not yet", "hold on"]

    # Check for explicit positive responses
    if any(indicator in user_input for indicator in positive_indicators):
        return True

    # Check for explicit negative responses
    if any(indicator in user_input for indicator in negative_indicators):
        return False

    # Default to positive if unclear (emergency situations should err on side of action)
    return True


def _parse_additional_help_response(user_input: str) -> bool:
    """
    Parse user's response to determine if they need additional help.

    Args:
        user_input: User's response (already lowercased)

    Returns:
        bool: True if they need more help, False if they're done
    """

    help_needed_indicators = ["yes", "yeah", "actually", "also", "another", "more", "help", "assist"]
    no_help_indicators = ["no", "nope", "nothing", "that's all", "all set", "good", "thanks", "thank you"]

    # Check for explicit help needed
    if any(indicator in user_input for indicator in help_needed_indicators):
        return True

    # Check for explicit no help needed
    if any(indicator in user_input for indicator in no_help_indicators):
        return False

    # If unclear, assume they don't need more help
    return False


def _execute_psap_contact(state: AgentState) -> Dict[str, Any]:
    """
    Execute PSAP contact for emergency situations.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    incident_type = state.get("incident_type")

    # Gather data from checklist for PSAP
    emergency_data = {
        "incident_type": incident_type.value,
        "severity": "HIGH" if incident_type == IncidentType.INJURY_ACCIDENT else "MEDIUM",
        "location": _get_checklist_value(state, "location") or "Location being determined",
        "vehicle_info": state.get("context", {}).get("vehicle_info", "Vehicle information available"),
        "occupant_info": _get_checklist_value(state, "occupant_count") or "Occupant information available",
        "additional_context": f"Gathered details: {_format_gathered_data(state)}"
    }

    # Add incident-specific data
    if incident_type == IncidentType.INJURY_ACCIDENT:
        emergency_data["injury_details"] = _get_checklist_value(state, "injury_severity") or "Injuries reported"
        emergency_data["vehicle_accessibility"] = _get_checklist_value(state, "vehicle_accessibility") or "Unknown"
    elif incident_type == IncidentType.ROAD_HAZARD:
        emergency_data["hazard_description"] = _get_checklist_value(state, "hazard_description") or "Road hazard reported"
        emergency_data["traffic_impact"] = _get_checklist_value(state, "traffic_impact") or "Unknown impact"

    # Contact PSAP
    psap_response = contact_psap(emergency_data)

    # Update metrics
    updated_state = update_metrics(state, "psap_contact_time", datetime.now().isoformat())

    # Create empathetic response message
    reassurance = random.choice(REASSURANCE_PHRASES)

    if incident_type == IncidentType.INJURY_ACCIDENT:
        response_message = (
            f"{reassurance} I've immediately contacted emergency services for you. "
            f"{psap_response['response_message']} "
            f"Your emergency reference number is {psap_response['psap_reference_id']}. "
            f"Medical assistance and police are being dispatched to your location. "
            f"Please follow these important instructions: {' '.join(psap_response['instructions'][:2])}"
        )
    else:
        response_message = (
            f"{reassurance} I've contacted the appropriate emergency services for you. "
            f"{psap_response['response_message']} "
            f"Your reference number is {psap_response['psap_reference_id']}. "
            f"They will be in touch with you shortly to provide further assistance."
        )

    # Add to conversation history
    updated_state = add_to_history(updated_state, "", response_message,
                                 action_taken="PSAP_CONTACTED", phase=ConversationPhase.ACTION_EXECUTION)

    # Update context with PSAP response
    updated_context = state.get("context", {}).copy()
    updated_context["psap_response"] = psap_response

    # Transition to follow-up phase
    updated_state = transition_phase(updated_state, ConversationPhase.FOLLOW_UP)

    return {
        "final_response": response_message,
        "current_phase": ConversationPhase.FOLLOW_UP,
        "context": updated_context,
        "next_action": NextAction.ASK_FOR_MORE_HELP,
        "history": updated_state["history"],
        "metrics": updated_state["metrics"]
    }


def _execute_rsa_contact(state: AgentState) -> Dict[str, Any]:
    """
    Execute RSA contact for roadside assistance.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    # Gather data from checklist for RSA
    rsa_data = {
        "incident_type": "RSA_NEED",
        "location": _get_checklist_value(state, "location") or "Location being determined",
        "vehicle_problem": _get_checklist_value(state, "vehicle_problem") or "Vehicle assistance needed",
        "safety_status": _get_checklist_value(state, "safety_status") or "Safety status unknown",
        "occupant_count": _get_checklist_value(state, "occupant_count") or "1",
        "additional_context": f"User reported: {state.get('user_input', '')}"
    }

    # Contact RSA
    rsa_response = contact_rsa(rsa_data)

    # Update metrics
    updated_state = update_metrics(state, "rsa_contact_time", datetime.now().isoformat())

    # Create empathetic response message
    reassurance = random.choice(REASSURANCE_PHRASES)

    response_message = (
        f"{reassurance} I've contacted our roadside assistance service for you. "
        f"{rsa_response['response_message']} "
        f"Your service request number is {rsa_response['rsa_reference_id']}. "
        f"A technician will be dispatched to your location and should arrive within "
        f"{rsa_response['estimated_arrival_time']}. "
        f"Please ensure you're in a safe location while you wait."
    )

    # Add to conversation history
    updated_state = add_to_history(updated_state, "", response_message,
                                 action_taken="RSA_CONTACTED", phase=ConversationPhase.ACTION_EXECUTION)

    # Update context with RSA response
    updated_context = state.get("context", {}).copy()
    updated_context["rsa_response"] = rsa_response

    # Transition to follow-up phase
    updated_state = transition_phase(updated_state, ConversationPhase.FOLLOW_UP)

    return {
        "final_response": response_message,
        "current_phase": ConversationPhase.FOLLOW_UP,
        "context": updated_context,
        "next_action": NextAction.ASK_FOR_MORE_HELP,
        "history": updated_state["history"],
        "metrics": updated_state["metrics"]
    }


def _get_checklist_value(state: AgentState, field_name: str) -> Optional[str]:
    """
    Get the value of a specific field from the data gathering checklist.

    Args:
        state: Current agent state
        field_name: Name of the field to retrieve

    Returns:
        Optional[str]: Value if found, None otherwise
    """

    checklist = state.get("data_gathering_checklist", [])
    for item in checklist:
        if item["field_name"] == field_name and item["status"] == DataGatheringStatus.COMPLETED:
            return item["value"]
    return None


def _format_gathered_data(state: AgentState) -> str:
    """
    Format all gathered data into a readable string.

    Args:
        state: Current agent state

    Returns:
        str: Formatted data string
    """

    checklist = state.get("data_gathering_checklist", [])
    completed_items = [item for item in checklist if item["status"] == DataGatheringStatus.COMPLETED]

    if not completed_items:
        return "No additional details gathered"

    formatted_data = []
    for item in completed_items:
        formatted_data.append(f"{item['display_name']}: {item['value']}")

    return "; ".join(formatted_data)
