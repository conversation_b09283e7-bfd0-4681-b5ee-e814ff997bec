"""
Workflow Nodes for Bosch eCall Emergency Communication System

This module contains the Python functions that represent the steps in the
LangGraph workflow. Each function takes the current state as input and
returns a dictionary with updated state values.
"""

from typing import Dict, Any
from datetime import datetime
import re

try:
    from .state import AgentState, IncidentType, NextAction, add_to_history, update_metrics
    from ..services.psap_service import contact_psap
    from ..services.rsa_service import contact_rsa
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from agent.state import AgentState, IncidentType, NextAction, add_to_history, update_metrics
    from services.psap_service import contact_psap
    from services.rsa_service import contact_rsa


def start_interaction(state: AgentState) -> Dict[str, Any]:
    """
    Entry point for the eCall workflow. Greets the user and asks for situation details.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values
    """
    
    greeting_message = (
        "Hello, this is Bosch Emergency Services. How can we help you today? "
    )
    
    # Update state with greeting and set next action
    updated_state = add_to_history(state, "", greeting_message)
    
    return {
        "final_response": greeting_message,
        "next_action": NextAction.ASK_FOR_CLARIFICATION,
        "history": updated_state["history"]
    }


def classify_incident(state: AgentState) -> Dict[str, Any]:
    """
    Core routing node that classifies the incident type based on user input.
    
    This function uses keyword matching to determine the type of emergency.
    In a future implementation, this would be enhanced with LLM-based classification.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values including incident_type
    """
    
    user_input = state.get("user_input", "").lower()
    
    # Define keyword patterns for different incident types
    injury_keywords = [
        "hurt", "injured", "pain", "bleeding", "unconscious", "broken", "fracture",
        "ambulance", "hospital", "medical", "emergency", "serious", "critical"
    ]
    
    accident_keywords = [
        "crash", "accident", "collision", "hit", "impact", "airbag", "damage",
        "fender bender", "rear end", "side impact"
    ]
    
    rsa_keywords = [
        "flat tire", "tire", "battery", "dead battery", "won't start", "engine",
        "breakdown", "mechanical", "tow", "stuck", "lockout", "locked out",
        "keys", "fuel", "gas", "empty tank"
    ]
    
    hazard_keywords = [
        "debris", "object", "road", "hazard", "dangerous", "blocking", "obstacle",
        "fallen tree", "rocks", "construction", "pothole"
    ]
    
    # Count keyword matches for each category
    injury_score = sum(1 for keyword in injury_keywords if keyword in user_input)
    accident_score = sum(1 for keyword in accident_keywords if keyword in user_input)
    rsa_score = sum(1 for keyword in rsa_keywords if keyword in user_input)
    hazard_score = sum(1 for keyword in hazard_keywords if keyword in user_input)
    
    # Determine incident type based on highest score
    scores = {
        IncidentType.INJURY_ACCIDENT: injury_score + (accident_score * 0.5),
        IncidentType.LIGHT_ACCIDENT: accident_score if injury_score == 0 else 0,
        IncidentType.RSA_NEED: rsa_score,
        IncidentType.ROAD_HAZARD: hazard_score
    }
    
    # Find the highest scoring incident type
    max_score = max(scores.values())
    
    if max_score == 0:
        incident_type = IncidentType.UNKNOWN
        confidence = 0.0
    else:
        incident_type = max(scores, key=scores.get)
        confidence = min(max_score / 3.0, 1.0)  # Normalize confidence score
    
    # Update metrics with classification confidence
    updated_state = update_metrics(state, "classification_confidence", confidence)
    
    return {
        "incident_type": incident_type,
        "metrics": updated_state["metrics"]
    }


def process_injury_accident(state: AgentState) -> Dict[str, Any]:
    """
    Handles the "Accident with Injury" scenario (Scenario 4).
    This is the highest priority emergency type.
    
    Args:
        state: Current agent state
    
    Returns:
        Dict with updated state values
    """
    
    # Prepare emergency data for PSAP
    emergency_data = {
        "incident_type": "INJURY_ACCIDENT",
        "severity": "HIGH",
        "location": state.get("context", {}).get("location", "Location being determined"),
        "vehicle_info": state.get("context", {}).get("vehicle_info", "Vehicle information available"),
        "occupant_info": "Injuries reported - immediate medical attention required",
        "additional_context": f"User reported: {state.get('user_input', '')}"
    }
    
    # Contact PSAP (emergency services)
    psap_response = contact_psap(emergency_data)
    
    # Update metrics with PSAP contact time
    updated_state = update_metrics(state, "psap_contact_time", datetime.now().isoformat())
    
    # Prepare response message
    response_message = (
        f"I understand this is a serious situation with injuries. I've immediately contacted "
        f"emergency services for you. {psap_response['response_message']} "
        f"Your emergency reference number is {psap_response['psap_reference_id']}. "
        f"Please follow these instructions: "
        f"{' '.join(psap_response['instructions'][:2])} "
        f"Emergency services will contact you directly if they need additional information."
    )
    
    # Add to conversation history
    updated_history_state = add_to_history(state, state.get("user_input", ""), response_message)
    
    return {
        "final_response": response_message,
        "is_complete": True,
        "next_action": NextAction.END_INTERACTION,
        "context": {**state.get("context", {}), "psap_response": psap_response},
        "history": updated_history_state["history"],
        "metrics": updated_state["metrics"]
    }


def process_light_accident(state: AgentState) -> Dict[str, Any]:
    """
    Handles Scenario 1 - Light accident without injuries.
    Contacts PSAP with lower urgency level.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    # Prepare accident data for PSAP
    accident_data = {
        "incident_type": "LIGHT_ACCIDENT",
        "severity": "MEDIUM",
        "location": state.get("context", {}).get("location", "Location being determined"),
        "vehicle_info": state.get("context", {}).get("vehicle_info", "Vehicle information available"),
        "occupant_info": "No injuries reported",
        "additional_context": f"User reported: {state.get('user_input', '')}"
    }

    # Contact PSAP
    psap_response = contact_psap(accident_data)

    # Update metrics
    updated_state = update_metrics(state, "psap_contact_time", datetime.now().isoformat())

    # Prepare response message
    response_message = (
        f"I understand you've been in an accident. I'm glad to hear there are no injuries. "
        f"I've contacted the appropriate authorities for you. {psap_response['response_message']} "
        f"Your reference number is {psap_response['psap_reference_id']}. "
        f"While you wait, please ensure your vehicle is in a safe location and your hazard lights are on if possible."
    )

    # Add to conversation history
    updated_history_state = add_to_history(state, state.get("user_input", ""), response_message)

    return {
        "final_response": response_message,
        "is_complete": True,
        "next_action": NextAction.END_INTERACTION,
        "context": {**state.get("context", {}), "psap_response": psap_response},
        "history": updated_history_state["history"],
        "metrics": updated_state["metrics"]
    }


def process_road_hazard(state: AgentState) -> Dict[str, Any]:
    """
    Handles Scenario 2 - Objects on the road or road hazards.
    Contacts PSAP to report the hazard.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    # Prepare hazard data for PSAP
    hazard_data = {
        "incident_type": "ROAD_HAZARD",
        "severity": "MEDIUM",
        "location": state.get("context", {}).get("location", "Location being determined"),
        "vehicle_info": state.get("context", {}).get("vehicle_info", "Vehicle information available"),
        "occupant_info": "Occupants safe",
        "additional_context": f"Road hazard reported: {state.get('user_input', '')}"
    }

    # Contact PSAP
    psap_response = contact_psap(hazard_data)

    # Update metrics
    updated_state = update_metrics(state, "psap_contact_time", datetime.now().isoformat())

    # Prepare response message
    response_message = (
        f"Thank you for reporting this road hazard. I've notified the appropriate authorities "
        f"to address this safety concern. {psap_response['response_message']} "
        f"Your report reference number is {psap_response['psap_reference_id']}. "
        f"Please drive carefully and avoid the hazard if possible."
    )

    # Add to conversation history
    updated_history_state = add_to_history(state, state.get("user_input", ""), response_message)

    return {
        "final_response": response_message,
        "is_complete": True,
        "next_action": NextAction.END_INTERACTION,
        "context": {**state.get("context", {}), "psap_response": psap_response},
        "history": updated_history_state["history"],
        "metrics": updated_state["metrics"]
    }


def process_rsa_request(state: AgentState) -> Dict[str, Any]:
    """
    Handles Scenario 3 - Customer in need of roadside assistance.
    Contacts RSA service provider.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    user_input = state.get("user_input", "").lower()

    # Determine specific assistance type based on user input
    assistance_type = "GENERAL"
    if any(keyword in user_input for keyword in ["flat", "tire", "puncture"]):
        assistance_type = "FLAT_TIRE"
    elif any(keyword in user_input for keyword in ["battery", "dead", "won't start", "jump"]):
        assistance_type = "DEAD_BATTERY"
    elif any(keyword in user_input for keyword in ["locked", "keys", "lockout"]):
        assistance_type = "LOCKOUT"
    elif any(keyword in user_input for keyword in ["tow", "towing", "can't drive"]):
        assistance_type = "TOW_NEEDED"
    elif any(keyword in user_input for keyword in ["engine", "mechanical", "breakdown"]):
        assistance_type = "ENGINE_TROUBLE"
    elif any(keyword in user_input for keyword in ["fuel", "gas", "empty"]):
        assistance_type = "FUEL_DELIVERY"

    # Prepare RSA request data
    rsa_data = {
        "assistance_type": assistance_type,
        "location": state.get("context", {}).get("location", "Location being determined"),
        "vehicle_info": state.get("context", {}).get("vehicle_info", "Vehicle information available"),
        "customer_info": state.get("context", {}).get("customer_info", "Customer information available"),
        "urgency": "STANDARD",
        "additional_context": f"Customer reported: {state.get('user_input', '')}"
    }

    # Contact RSA
    rsa_response = contact_rsa(rsa_data)

    # Update metrics
    updated_state = update_metrics(state, "rsa_contact_time", datetime.now().isoformat())

    # Prepare response message
    response_message = (
        f"I understand you need roadside assistance. I've contacted our service provider for you. "
        f"A {rsa_response['technician_type']} has been dispatched and will arrive in approximately "
        f"{rsa_response['estimated_arrival']}. Your service ticket number is {rsa_response['service_ticket']}. "
        f"The technician will call you at {rsa_response['technician_contact']} when they arrive. "
        f"Please follow these instructions while you wait: {' '.join(rsa_response['service_instructions'][:2])}"
    )

    # Add to conversation history
    updated_history_state = add_to_history(state, state.get("user_input", ""), response_message)

    return {
        "final_response": response_message,
        "is_complete": True,
        "next_action": NextAction.END_INTERACTION,
        "context": {**state.get("context", {}), "rsa_response": rsa_response},
        "history": updated_history_state["history"],
        "metrics": updated_state["metrics"]
    }


def handle_unknown(state: AgentState) -> Dict[str, Any]:
    """
    Fallback node for when the incident type cannot be determined.
    Asks for clarification from the user.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    clarification_message = (
        "I want to make sure I get you the right help. Could you please provide more details about your situation? "
        "For example: Are you injured or is anyone hurt? Have you been in an accident? "
        "Do you need roadside assistance like a tire change or jump start? "
        "Or are you reporting a road hazard or dangerous condition?"
    )

    # Add to conversation history
    updated_history_state = add_to_history(state, state.get("user_input", ""), clarification_message)

    return {
        "final_response": clarification_message,
        "next_action": NextAction.ASK_FOR_CLARIFICATION,
        "history": updated_history_state["history"]
    }


def end_interaction(state: AgentState) -> Dict[str, Any]:
    """
    Final node that prepares a closing message and completes the workflow.

    Args:
        state: Current agent state

    Returns:
        Dict with updated state values
    """

    # Update resolution time metric
    updated_state = update_metrics(state, "resolution_time", datetime.now().isoformat())

    closing_message = (
        "Thank you for using Bosch Emergency Services. We've processed your request and the appropriate "
        "assistance has been arranged. If you need any additional help or have questions about your "
        "service request, please don't hesitate to contact us again. Stay safe!"
    )

    return {
        "final_response": closing_message,
        "is_complete": True,
        "metrics": updated_state["metrics"]
    }
