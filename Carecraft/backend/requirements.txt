annotated-types==0.7.0
anyio==4.9.0
asttokens==3.0.0
attrs==21.2.0
Automat==20.2.0
Babel==2.8.0
bcrypt==3.2.0
blinker==1.4
certifi==2020.6.20
cffi==1.15.1
chardet==4.0.0
click==8.0.3
cloud-init==24.4
colorama==0.4.4
comm==0.2.2
command-not-found==0.3
configobj==5.0.6
constantly==15.1.0
cryptography==3.4.8
dbus-python==1.2.18
debugpy==1.8.14
decorator==5.2.1
distro==1.7.0
distro-info==1.1+ubuntu0.2
exceptiongroup==1.2.2
executing==2.2.0
fastapi==0.115.13
GDAL==3.4.1
gyp==0.1
httplib2==0.20.2
hyperlink==21.0.0
idna==3.3
importlib-metadata==4.6.4
incremental==21.3.0
ipykernel==6.29.5
ipython==8.36.0
jedi==0.19.2
jeepney==0.7.1
Jinja2==3.0.3
joblib==1.3.2
jsonpatch==1.32
jsonpointer==2.0
jsonschema==3.2.0
jupyter_client==8.6.3
jupyter_core==5.7.2
keyring==23.5.0
launchpadlib==1.10.16
lazr.restfulclient==0.14.4
lazr.uri==1.0.6
llvmlite==0.42.0
MarkupSafe==2.0.1
matplotlib-inline==0.1.7
more-itertools==8.10.0
nest-asyncio==1.6.0
netifaces==0.11.0
numba==0.59.0
numpy==1.26.4
oauthlib==3.2.0
packaging==24.2
parso==0.8.4
pexpect==4.9.0
platformdirs==4.3.7
prompt_toolkit==3.0.50
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.4.8
pyasn1-modules==0.2.1
pycparser==2.21
pycurl==7.44.1
pydantic==2.11.7
pydantic_core==2.33.2
Pygments==2.16.1
PyGObject==3.42.1
PyHamcrest==2.0.2
PyJWT==2.3.0
pynndescent==0.5.11
pyOpenSSL==21.0.0
pyparsing==2.4.7
pyrsistent==0.18.1
pyserial==3.5
python-apt==2.4.0+ubuntu4
python-dateutil==2.9.0.post0
pytz==2022.1
PyYAML==5.4.1
pyzmq==26.4.0
radian==0.6.13
rchitect==0.4.7
requests==2.25.1
scikit-learn==1.4.0
scipy==1.12.0
SecretStorage==3.3.1
service-identity==18.1.0
six==1.16.0
sniffio==1.3.1
stack-data==0.6.3
starlette==0.46.2
systemd-python==234
threadpoolctl==3.2.0
tornado==6.4.2
tqdm==4.66.1
traitlets==5.14.3
Twisted==22.1.0
typing-inspection==0.4.1
typing_extensions==4.13.2
ubuntu-pro-client==8001
ufw==0.36.1
umap-learn==0.5.5
unattended-upgrades==0.1
urllib3==1.26.5
wadllib==1.3.6
wcwidth==0.2.6
zipp==1.0.0
zope.interface==5.4.0
