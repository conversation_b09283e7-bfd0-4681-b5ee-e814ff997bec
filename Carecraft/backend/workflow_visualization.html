<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bosch eCall System - Workflow Visualization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .workflow-container {
            padding: 40px;
        }

        .section {
            margin-bottom: 50px;
        }

        .section h2 {
            color: #1e3c72;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 10px;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .component {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .component:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #2a5298;
        }

        .component h3 {
            color: #1e3c72;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .component ul {
            list-style: none;
            padding-left: 0;
        }

        .component li {
            padding: 5px 0;
            color: #666;
        }

        .component li:before {
            content: "→ ";
            color: #2a5298;
            font-weight: bold;
        }

        .workflow-diagram {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow-x: auto;
        }

        .flow-step {
            display: inline-block;
            background: white;
            border: 2px solid #2a5298;
            border-radius: 10px;
            padding: 15px 20px;
            margin: 10px;
            min-width: 150px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .flow-step:hover {
            background: #2a5298;
            color: white;
            transform: scale(1.05);
        }

        .flow-step.start {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .flow-step.decision {
            background: #ffc107;
            border-color: #ffc107;
            border-radius: 50px;
        }

        .flow-step.emergency {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .flow-step.service {
            background: #17a2b8;
            color: white;
            border-color: #17a2b8;
        }

        .flow-step.end {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .arrow {
            display: inline-block;
            margin: 0 10px;
            font-size: 1.5em;
            color: #2a5298;
            vertical-align: middle;
        }

        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .scenario {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            transition: transform 0.3s ease;
        }

        .scenario:hover {
            transform: translateY(-5px);
        }

        .scenario h3 {
            font-size: 1.4em;
            margin-bottom: 15px;
        }

        .scenario-details {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .conversation-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .chat-message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-left: auto;
        }

        .ai-message {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }

        .system-message {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            text-align: center;
            font-style: italic;
        }

        .api-endpoints {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .endpoint {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }

        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .post { background: #28a745; }
        .get { background: #17a2b8; }
        .delete { background: #dc3545; }

        .interactive-demo {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: #fff;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            margin: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .workflow-diagram {
                text-align: center;
            }
            
            .flow-step {
                display: block;
                margin: 10px auto;
            }
            
            .arrow {
                display: block;
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Bosch eCall Emergency Communication System</h1>
            <p>AI-Driven Emergency Response Workflow Visualization</p>
        </div>

        <div class="workflow-container">
            <!-- System Architecture -->
            <div class="section">
                <h2>🏗️ System Architecture</h2>
                <div class="architecture-grid">
                    <div class="component">
                        <h3>📊 Agent State Management</h3>
                        <ul>
                            <li>Session tracking</li>
                            <li>Conversation history</li>
                            <li>Incident classification</li>
                            <li>Performance metrics</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🔄 Workflow Nodes</h3>
                        <ul>
                            <li>Start interaction</li>
                            <li>Classify incident</li>
                            <li>Process emergencies</li>
                            <li>Handle assistance</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🚨 PSAP Service</h3>
                        <ul>
                            <li>Emergency dispatch</li>
                            <li>Priority handling</li>
                            <li>Reference tracking</li>
                            <li>Status updates</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🔧 RSA Service</h3>
                        <ul>
                            <li>Roadside assistance</li>
                            <li>Technician dispatch</li>
                            <li>Service coordination</li>
                            <li>Cost estimation</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🌐 FastAPI Server</h3>
                        <ul>
                            <li>REST API endpoints</li>
                            <li>Session management</li>
                            <li>Real-time processing</li>
                            <li>Error handling</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🧠 LangGraph Workflow</h3>
                        <ul>
                            <li>State machine</li>
                            <li>Conditional routing</li>
                            <li>Decision trees</li>
                            <li>Flow orchestration</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Main Workflow -->
            <div class="section">
                <h2>🔄 Main Workflow Process</h2>
                <div class="workflow-diagram">
                    <div class="flow-step start">🚀 Start Session</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">👋 Greeting</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">📝 User Input</div>
                    <span class="arrow">→</span>
                    <div class="flow-step decision">🤔 Classify Incident</div>
                    <br><br>
                    <div class="flow-step emergency">🚑 Injury Accident</div>
                    <div class="flow-step service">🚗 Light Accident</div>
                    <div class="flow-step service">⚠️ Road Hazard</div>
                    <div class="flow-step service">🔧 RSA Need</div>
                    <br><br>
                    <span class="arrow">↓</span>
                    <br>
                    <div class="flow-step emergency">📞 Contact PSAP</div>
                    <div class="flow-step service">📞 Contact RSA</div>
                    <br><br>
                    <span class="arrow">↓</span>
                    <br>
                    <div class="flow-step end">✅ End Session</div>
                </div>
            </div>

            <!-- Emergency Scenarios -->
            <div class="section">
                <h2>🚨 Emergency Scenarios</h2>
                <div class="scenario-grid">
                    <div class="scenario">
                        <h3>🚑 Scenario 4: Injury Accident</h3>
                        <p><strong>Trigger:</strong> "hurt", "injured", "medical", "ambulance"</p>
                        <div class="scenario-details">
                            <p><strong>Priority:</strong> CRITICAL</p>
                            <p><strong>Action:</strong> Immediate PSAP contact</p>
                            <p><strong>Response:</strong> Emergency services dispatch</p>
                            <p><strong>ETA:</strong> 8-12 minutes</p>
                        </div>
                    </div>
                    <div class="scenario">
                        <h3>🚗 Scenario 1: Light Accident</h3>
                        <p><strong>Trigger:</strong> "accident", "crash" (no injury keywords)</p>
                        <div class="scenario-details">
                            <p><strong>Priority:</strong> STANDARD</p>
                            <p><strong>Action:</strong> Police notification</p>
                            <p><strong>Response:</strong> Police unit dispatch</p>
                            <p><strong>ETA:</strong> 15-20 minutes</p>
                        </div>
                    </div>
                    <div class="scenario">
                        <h3>⚠️ Scenario 2: Road Hazard</h3>
                        <p><strong>Trigger:</strong> "debris", "hazard", "blocking"</p>
                        <div class="scenario-details">
                            <p><strong>Priority:</strong> MEDIUM</p>
                            <p><strong>Action:</strong> Highway maintenance alert</p>
                            <p><strong>Response:</strong> Maintenance team dispatch</p>
                            <p><strong>ETA:</strong> 20-30 minutes</p>
                        </div>
                    </div>
                    <div class="scenario">
                        <h3>🔧 Scenario 3: Roadside Assistance</h3>
                        <p><strong>Trigger:</strong> "flat tire", "battery", "breakdown"</p>
                        <div class="scenario-details">
                            <p><strong>Priority:</strong> STANDARD</p>
                            <p><strong>Action:</strong> RSA service contact</p>
                            <p><strong>Response:</strong> Technician dispatch</p>
                            <p><strong>ETA:</strong> 30-45 minutes</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conversation Demo -->
            <div class="section">
                <h2>💬 Conversation Process Example</h2>
                <div class="conversation-demo">
                    <h3>🚑 Injury Accident Scenario</h3>

                    <div class="system-message">
                        🚀 Session Started - ID: emergency-001
                    </div>

                    <div class="ai-message">
                        <strong>Bosch eCall:</strong> Hello, this is Bosch Emergency Services. We've detected a potential emergency situation with your vehicle. I'm here to help you get the assistance you need. Can you please tell me what's happening right now?
                    </div>

                    <div class="user-message">
                        <strong>User:</strong> I've been in a crash and I'm hurt, need ambulance
                    </div>

                    <div class="system-message">
                        🤔 Classifying incident... → INJURY_ACCIDENT (Confidence: 0.95)
                    </div>

                    <div class="system-message">
                        📞 Contacting PSAP... → Reference: PSAP-20241224T143052
                    </div>

                    <div class="ai-message">
                        <strong>Bosch eCall:</strong> I understand this is a serious situation with injuries. I've immediately contacted emergency services for you. Emergency services dispatched immediately. Ambulance and police en route. Estimated arrival: 8-12 minutes. Your emergency reference number is PSAP-20241224T143052. Please stay with your vehicle if safe to do so and keep your phone available for emergency services contact.
                    </div>

                    <div class="system-message">
                        ✅ Session Complete - Emergency services contacted
                    </div>
                </div>

                <div class="conversation-demo">
                    <h3>🔧 Roadside Assistance Scenario</h3>

                    <div class="system-message">
                        🚀 Session Started - ID: rsa-002
                    </div>

                    <div class="ai-message">
                        <strong>Bosch eCall:</strong> Hello, this is Bosch Emergency Services. We've detected a potential emergency situation with your vehicle. I'm here to help you get the assistance you need. Can you please tell me what's happening right now?
                    </div>

                    <div class="user-message">
                        <strong>User:</strong> I have a flat tire and need help
                    </div>

                    <div class="system-message">
                        🤔 Classifying incident... → RSA_NEED (Confidence: 0.88)
                    </div>

                    <div class="system-message">
                        🔧 Contacting RSA... → Ticket: RSA-20241224T143125
                    </div>

                    <div class="ai-message">
                        <strong>Bosch eCall:</strong> I understand you need roadside assistance. I've contacted our service provider for you. A Mobile Tire Specialist has been dispatched and will arrive in approximately 30-45 minutes. Your service ticket number is RSA-20241224T143125. The technician will call you at ******-RSA-3125 when they arrive. Please stay with your vehicle in a safe location and turn on hazard lights if safe to do so.
                    </div>

                    <div class="system-message">
                        ✅ Session Complete - Roadside assistance arranged
                    </div>
                </div>
            </div>

            <!-- API Endpoints -->
            <div class="section">
                <h2>🌐 API Endpoints</h2>
                <div class="api-endpoints">
                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <strong>/e-call/session</strong>
                        <p>Start a new emergency communication session</p>
                    </div>
                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <strong>/e-call/session/{session_id}</strong>
                        <p>Continue conversation in existing session</p>
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <strong>/e-call/session/{session_id}/status</strong>
                        <p>Get current session status and metrics</p>
                    </div>
                    <div class="endpoint">
                        <span class="method delete">DELETE</span>
                        <strong>/e-call/session/{session_id}</strong>
                        <p>End session and cleanup resources</p>
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <strong>/e-call/sessions</strong>
                        <p>List all active emergency sessions</p>
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <strong>/workflow/visualization</strong>
                        <p>Get workflow structure information</p>
                    </div>
                </div>
            </div>

            <!-- Technical Flow -->
            <div class="section">
                <h2>⚙️ Technical Data Flow</h2>
                <div class="workflow-diagram">
                    <h3>1. Session Initialization</h3>
                    <div class="flow-step">Frontend</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">POST /e-call/session</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">create_initial_state()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">start_interaction()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">Response + Session ID</div>

                    <br><br><h3>2. Message Processing</h3>
                    <div class="flow-step">User Message</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">POST /e-call/session/{id}</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">classify_incident()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step decision">Route by Type</div>

                    <br><br><h3>3. Service Integration</h3>
                    <div class="flow-step emergency">process_injury_accident()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step emergency">contact_psap()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">PSAP Response</div>

                    <br>
                    <div class="flow-step service">process_rsa_request()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step service">contact_rsa()</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">RSA Response</div>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="section">
                <div class="interactive-demo">
                    <h2>🎮 Try the System</h2>
                    <p>Start the FastAPI server and test these scenarios:</p>
                    <button class="demo-button" onclick="showDemo('injury')">🚑 Test Injury Accident</button>
                    <button class="demo-button" onclick="showDemo('rsa')">🔧 Test Flat Tire</button>
                    <button class="demo-button" onclick="showDemo('hazard')">⚠️ Test Road Hazard</button>
                    <button class="demo-button" onclick="showDemo('api')">📡 View API Docs</button>
                </div>
            </div>

            <!-- System Status -->
            <div class="section">
                <h2>📊 System Status</h2>
                <div class="architecture-grid">
                    <div class="component">
                        <h3>✅ Core Components</h3>
                        <ul>
                            <li>State Management: Ready</li>
                            <li>Workflow Nodes: Implemented</li>
                            <li>LangGraph Integration: Complete</li>
                            <li>FastAPI Server: Functional</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>✅ Services</h3>
                        <ul>
                            <li>PSAP Service: Mock Ready</li>
                            <li>RSA Service: Mock Ready</li>
                            <li>Classification: Keyword-based</li>
                            <li>Error Handling: Implemented</li>
                        </ul>
                    </div>
                    <div class="component">
                        <h3>🔮 Future Enhancements</h3>
                        <ul>
                            <li>LLM Integration (GPT-4)</li>
                            <li>Real Service APIs</li>
                            <li>Voice Support</li>
                            <li>Multi-language</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDemo(type) {
            const demos = {
                injury: 'curl -X POST "http://localhost:8000/e-call/session" -H "Content-Type: application/json" -d \'{"initial_message": "I\'ve been in a crash and I\'m hurt"}\'',
                rsa: 'curl -X POST "http://localhost:8000/e-call/session" -H "Content-Type: application/json" -d \'{"initial_message": "I have a flat tire"}\'',
                hazard: 'curl -X POST "http://localhost:8000/e-call/session" -H "Content-Type: application/json" -d \'{"initial_message": "There\'s debris on the highway"}\'',
                api: 'http://localhost:8000/docs'
            };

            if (type === 'api') {
                alert('Open your browser to: ' + demos[type]);
            } else {
                alert('Run this command in your terminal:\n\n' + demos[type]);
            }
        }

        // Add hover effects and animations
        document.addEventListener('DOMContentLoaded', function() {
            const components = document.querySelectorAll('.component');
            components.forEach(component => {
                component.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
