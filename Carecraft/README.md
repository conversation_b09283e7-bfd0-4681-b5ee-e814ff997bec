### Frontend

#### Download and install Node.js:
nvm install 22

#### Verify the Node.js version:
node -v # Should print "v22.16.0".
nvm current # Should print "v22.16.0".

#### Verify npm version:
npm -v # Should print "10.9.2".


#### now install packages (from frontend dir)
npm i


#### For reference (first scaffolding)
```
npm create vite@latest -- --template react-ts
```



- #### Vite
    - https://vite.dev/guide/
- #### React Docs
    - https://react.dev/learn
- #### Tailwind CSS Docs
    - https://tailwindcss.com/docs
- #### TypeScript Docs
    - https://www.typescriptlang.org/docs/
- #### Axios Docs
    - https://axios-http.com/docs/intro
- #### Radix UI Docs
    - https://www.radix-ui.com/primitives/docs/overview/introduction
- #### Heroicons Docs
    - https://heroicons.com/
- #### TipTap Docs
    - https://tiptap.dev/docs/editor/getting-started/overview
    - YouTube video: https://www.youtube.com/watch?v=bBCVI2e18dE
    - NodeViews https://tiptap.dev/docs/editor/extensions/custom-extensions/node-views
- #### D3.js Docs
    - https://d3js.org/getting-started
- #### Mermaid Docs
    - https://mermaid.js.org/intro/


### Python backend

#### Setup (from backend dir)

```bash
python3.12 -m venv .venv
source .venv/bin/activate
```

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

```bash
#after each package install
pip freeze > requirements.txt
```


```bash
fastapi dev
```